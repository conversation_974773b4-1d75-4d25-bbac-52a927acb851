#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试港口相关分析功能
"""

import os
import asyncio
from datetime import datetime, timedelta
import sys

# 添加项目根目录到Python路径
sys.path.append('/Volumes/PSSD/code_files/mcp-cms')

from utils.fastapi_apps.fastapi_cms_simplified import analyze_data_with_pandasai

async def test_port_fields():
    """测试港口字段分析"""
    try:
        print("🔍 开始测试港口字段分析...")
        
        # 设置测试参数 - 检查港口字段（使用正确的字段名）
        question = "显示job_pol、bill_pol和bill_pod字段的前10条记录，包括这些字段的值"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 港口字段分析完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 港口字段分析测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_port_statistics():
    """测试港口统计分析"""
    try:
        print("\n🔍 开始测试港口统计分析...")
        
        # 设置测试参数 - 港口统计（使用正确的字段名）
        question = "统计bill_pol字段的不同值及其出现次数，显示前10个最常见的港口"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 港口统计分析完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 港口统计分析测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_port_conventions():
    """测试港口约定应用"""
    try:
        print("\n🔍 开始测试港口约定应用...")
        
        # 设置测试参数 - 应用港口约定
        question = """
        根据港口约定规则分析起运港数据：
        1. 优先使用bill_pol（提单起运港）
        2. 如果bill_pol为空，则使用job_pol（航次始发港）
        3. 统计处理后的起运港分布情况
        """
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 港口约定应用分析完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 港口约定应用测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_port_fields())
    asyncio.run(test_port_statistics())
    asyncio.run(test_port_conventions())
