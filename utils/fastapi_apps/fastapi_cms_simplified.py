#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FastAPI CMS Simplified - 企业内容管理系统精简版本

只保留核心功能：
1. Job/Booking数据导出
2. 人员/公司/部门名称查询
3. 系统监控
"""

import os
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager
import threading
import time

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, field_validator
import uvicorn
import pandas as pd
from pandasai import SmartDataframe, SmartDatalake
from pandasai.llm import AzureOpenAI
from pandasai.connectors import MySQLConnector
from sqlalchemy import create_engine

# 导入数据库操作函数
from utils.database.db_pro2_basic import (
    search_company_by_part_name,
    search_user_by_part_name,
    search_department_by_part_name,
    get_sea_air_profit_with_transhipment,
    get_job_details_with_transhipment
)

from utils.basic.logger_config import setup_logger
from utils.basic.optimized_export import export_to_oss, apply_column_mapping
from utils.basic.data_cache_manager import (
    global_cache_manager,
    get_ai_analysis_data,
    set_ai_analysis_data
)
from utils.basic.data_conn_unified import (
    MYSQL_USER,
    MYSQL_PASSWORD,
    MYSQL_DB_MCP,
    get_ssh_manager
)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="info",
    log_to_console=True,
    log_to_file=True
)

# 全局调度器变量
_profit_scheduler = None


# 使用全局缓存管理器，支持跨端点缓存共享

# PandasAI 配置
def get_pandasai_llm():
    """获取配置好的 PandasAI LLM 实例"""
    try:
        llm = AzureOpenAI(
            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
            api_base=os.getenv("AZURE_OPENAI_ENDPOINT"),
            api_version=os.getenv("AZURE_API_VERSION"),
            deployment_name=os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4.1-mini"),
        )
        return llm
    except Exception as e:
        logger.error(f"初始化 PandasAI LLM 失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"AI 分析服务初始化失败: {str(e)}"
        )


def get_mysql_config():
    """获取MySQL连接配置"""
    try:
        ssh_manager = get_ssh_manager()
        conn_params = ssh_manager.get_connection_params()

        if not isinstance(conn_params, dict):
            raise ValueError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")

        return {
            "host": conn_params['mysql_host'],
            "port": conn_params['mysql_port'],
            "database": MYSQL_DB_MCP,
            "username": MYSQL_USER,
            "password": MYSQL_PASSWORD,
        }
    except Exception as e:
        logger.error(f"获取MySQL配置失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"数据库连接配置失败: {str(e)}"
        )


async def analyze_data_with_pandasai(
    question: str,
    language: str = "zh",
    session_id: str = None,
    begin_date: str = None,
    end_date: str = None,
    data_type: str = "booking",
    pro2_system_id: int = 86532  # 默认为青岛公司，None表示不过滤
) -> str:
    """使用 PandasAI 分析MySQL数据源中的数据"""
    try:
        # 获取MySQL连接配置
        mysql_config = get_mysql_config()
        logger.info(f"连接到MySQL数据库: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")

        # 获取 LLM 实例
        llm = get_pandasai_llm()

        # 构建where条件
        where_conditions = []
        # 过滤公司数据（如果指定了pro2_system_id）
        if pro2_system_id is not None:
            where_conditions.append(["pro2_system_id", "=", str(pro2_system_id)])
        if session_id:
            where_conditions.append(["session_id", "=", session_id])
        if begin_date:
            where_conditions.append(["job_date", ">=", begin_date])
        if end_date:
            where_conditions.append(["job_date", "<=", end_date])

        # 根据数据类型选择表
        table_name = "t_booking_details" if data_type == "booking" else "t_job_details"

        # 使用项目现有的数据库连接方法
        from utils.basic.data_conn_unified import connect_mysql

        # 构建SQL查询
        sql_query = f"SELECT * FROM {table_name}"
        if where_conditions:
            where_clauses = []
            for condition in where_conditions:
                field, operator, value = condition
                where_clauses.append(f"{field} {operator} '{value}'")
            sql_query += " WHERE " + " AND ".join(where_clauses)

        # 使用项目现有的数据库连接获取数据
        connection = connect_mysql(database=MYSQL_DB_MCP)
        try:
            data_df = pd.read_sql(sql_query, connection)
            logger.info(f"从{table_name}表读取了{len(data_df)}条记录")
        finally:
            connection.close()

        # 检查数据是否为空
        if data_df.empty:
            return f"未找到符合条件的数据。请检查日期范围和筛选条件。"

        # 创建 SmartDataframe 来处理数据
        df = SmartDataframe(
            data_df,
            config={
                "llm": llm,
                "enable_cache": False,
                "verbose": True,
                "enforce_privacy": False,  # 关闭隐私保护以允许导入模块
                "save_charts": False,
                "open_charts": False,
                "custom_whitelisted_dependencies": ["pandas", "numpy", "matplotlib", "seaborn"],
            },
        )

        logger.info(f"成功创建SmartDataframe，连接到{table_name}表")

        # 获取公司名称映射和系统所在地
        company_mapping = {
            86532: {"name": "青岛公司", "code": "QDO", "is_system_location": True},
            86021: {"name": "上海公司", "code": "SHA", "is_system_location": False},
            8103: {"name": "东京公司", "code": "TKY", "is_system_location": False},
            852: {"name": "香港公司", "code": "HKG", "is_system_location": False}
        }

        # 动态确定系统所在地
        system_location_company = None
        for comp_id, comp_info in company_mapping.items():
            if comp_info["is_system_location"]:
                system_location_company = comp_info
                break

        if not system_location_company:
            system_location_company = {"name": "青岛公司", "code": "QDO"}  # 默认值

        current_company_info = company_mapping.get(pro2_system_id, {"name": f"公司ID {pro2_system_id}", "code": "UNKNOWN"})
        current_company = f"{current_company_info['name']}（{current_company_info['code']}）"
        system_location = f"{system_location_company['name']}（{system_location_company['code']}）"

        # 表结构说明
        if data_type == "booking":
            table_structure = """
            t_booking_details表结构说明：
            - id: 主键ID
            - session_id: 会话ID，用于数据隔离
            - job_type_cn: 业务类型中文名（海运出口/海运进口/海运三角/空运出口/空运进口）
            - job_date: 工作档日期
            - job_no: 工作档编号
            - bkbl_no: 订舱/提单编号
            - client_name: 客户名称
            - vessel: 船名/航班号
            - voyage: 航次/航班日期
            - job_pol: 航次始发港
            - bill_pol: 提单起运港
            - bill_pod: 提单卸货港
            - service_mode: 服务模式（LCL/FCL/BUY-CONSOL/AIR）
            - lcl_rt: 拼箱计费重量
            - teu: TEU数量
            - air_weight: 空运计费重量
            - income: 收入
            - cost: 成本
            - profit: 利润
            - transhipment_profit: 转运利润
            - total_business_profit: 业务总利润
            - is_freehand: 是否自揽货（0/1）
            - salesman_name: 业务员
            - salesman_department: 业务员部门名称
            - operator_name: 操作员名称
            - operator_department: 操作员部门名称
            - coloader_name: Coloader名称
            - job_handling_agent: 工作档代理
            - bl_handling_agent: 操作代理
            - is_transhipment: 是否转运（0/1）
            - pro2_system_id: 系统ID，用于区分不同公司
            """

            port_conventions = f"""
            港口约定规则（Booking数据）：
            - 当前系统所在地港口为{system_location}
            - 港口字段处理规则：
              1. 起运港确定：优先使用bill_pol（提单起运港），如果为空则使用job_pol（航次始发港）
              2. 目的港确定：
                 * 进口业务：如果bill_pod（提单卸货港）为空，则默认为系统所在地{system_location_company['code']}
                 * 出口业务：直接使用bill_pod（提单卸货港）
              3. 在分析涉及港口的数据时，必须按照以上规则处理港口字段
            """
        else:
            table_structure = """
            t_job_details表结构说明：
            - id: 主键ID
            - session_id: 会话ID，用于数据隔离
            - job_type_cn: 业务类型中文名（海运出口/海运进口/海运三角/空运出口/空运进口）
            - job_date: 工作档日期
            - job_no: 工作档编号
            - vessel: 船名/航班号
            - voyage: 航次/航班信息
            - pol_code: 起运港代码
            - pod_code: 卸货港代码
            - bk_count: 订舱数量
            - cbm: 总体积
            - rt: 总计费数量
            - teu: 总TEU
            - income: 收入
            - cost: 成本
            - profit: 毛利润
            - transhipment_profit: 转运利润
            - total_business_profit: 业务总利润
            - operator_name: 操作员名称
            - job_handling_agent: 工作档代理名称
            - nomi_count: 指定货票数
            - nomi_rt: 指定货rt总和
            - is_consolidation: 是否集拼
            - bill_count: 提单数量
            - consolidation_20: 集拼20尺柜数量
            - consolidation_40: 集拼40尺柜数量
            - operator_department: 操作员部门名称
            - is_op_finished: 操作是否完成
            - is_checked: 是否已审核
            - pro2_system_id: 系统ID，用于区分不同公司
            """

            port_conventions = f"""
            港口约定规则（Job数据）：
            - 当前系统所在地港口为{system_location}
            - 港口字段处理规则：
              1. 起运港：直接使用pol_code字段
              2. 目的港：
                 * 进口业务：如果pod_code（卸货港）为空，则默认为系统所在地{system_location_company['code']}
                 * 出口业务：直接使用pod_code字段
              3. 在分析涉及港口的数据时，必须按照以上规则处理港口字段
            """

        # 根据语言调整问题
        if language == "zh":
            analysis_prompt = f"""
            请用中文回答以下问题：{question}

            数据说明：
            - 当前分析的是{current_company}的业务数据（pro2_system_id={pro2_system_id}）
            - 当前数据表：{table_name}
            - 数据记录数：{len(data_df)}条
            - 数据已经过预处理，可以直接使用，无需额外去重

            {table_structure}

            {port_conventions}

            重要分析规则：
            1. 在进行港口相关分析时，必须严格按照港口约定规则处理字段
            2. 对于空值字段，按照约定规则进行默认值填充
            3. 确保分析结果的准确性和一致性

            请分析数据并提供简洁明了的结果。确保你的代码能正确执行并返回结果。
            如果需要计算统计数据，请使用pandas的聚合函数如sum()、mean()、count()等。
            """
        else:
            # 英文公司名称
            current_company_en = f"{current_company_info['name']} ({current_company_info['code']})"
            system_location_en = f"{system_location_company['name']} ({system_location_company['code']})"

            if data_type == "booking":
                table_structure_en = """
                t_booking_details Table Structure:
                - id: Primary key ID
                - session_id: Session ID for data isolation
                - job_type_cn: Business type in Chinese (Sea Export/Sea Import/Sea Triangle/Air Export/Air Import)
                - job_date: Job file date
                - job_no: Job file number
                - bkbl_no: Booking/BL number
                - client_name: Client name
                - vessel: Vessel name/flight number
                - voyage: Voyage/flight date
                - job_pol: Sailing port of loading
                - bill_pol: Bill of lading port of loading
                - bill_pod: Bill of lading port of discharge
                - service_mode: Service mode (LCL/FCL/BUY-CONSOL/AIR)
                - lcl_rt: LCL chargeable weight
                - teu: TEU quantity
                - air_weight: Air freight weight
                - income: Revenue
                - cost: Cost
                - profit: Profit
                - transhipment_profit: Transhipment profit
                - total_business_profit: Total business profit
                - is_freehand: Is free hand cargo (0/1)
                - salesman_name: Salesman
                - salesman_department: Salesman department
                - operator_name: Operator name
                - operator_department: Operator department
                - coloader_name: Coloader name
                - job_handling_agent: Job handling agent
                - bl_handling_agent: BL handling agent
                - is_transhipment: Is transhipment (0/1)
                - pro2_system_id: System ID for company distinction
                """

                port_conventions_en = f"""
                Port Convention Rules (Booking Data):
                - Current system location port is {system_location_en}
                - Port field processing rules:
                  1. Origin port determination: Use bill_pol (BL port of loading) first, if empty then use job_pol (sailing port of loading)
                  2. Destination port determination:
                     * Import business: If bill_pod (BL port of discharge) is empty, default to system location {system_location_company['code']}
                     * Export business: Use bill_pod (BL port of discharge) directly
                  3. When analyzing port-related data, must follow the above rules for port field processing
                """
            else:
                table_structure_en = """
                t_job_details Table Structure:
                - id: Primary key ID
                - session_id: Session ID for data isolation
                - job_type_cn: Business type in Chinese (Sea Export/Sea Import/Sea Triangle/Air Export/Air Import)
                - job_date: Job file date
                - job_no: Job file number
                - vessel: Vessel name/flight number
                - voyage: Voyage/flight information
                - pol_code: Port of loading code
                - pod_code: Port of discharge code
                - bk_count: Booking quantity
                - cbm: Total volume
                - rt: Total chargeable quantity
                - teu: Total TEU
                - income: Revenue
                - cost: Cost
                - profit: Gross profit
                - transhipment_profit: Transhipment profit
                - total_business_profit: Total business profit
                - operator_name: Operator name
                - job_handling_agent: Job handling agent name
                - nomi_count: Nominated cargo count
                - nomi_rt: Nominated cargo RT total
                - is_consolidation: Is consolidation
                - bill_count: Bill count
                - consolidation_20: 20ft consolidation container count
                - consolidation_40: 40ft consolidation container count
                - operator_department: Operator department
                - is_op_finished: Is operation finished
                - is_checked: Is checked
                - pro2_system_id: System ID for company distinction
                """

                port_conventions_en = f"""
                Port Convention Rules (Job Data):
                - Current system location port is {system_location_en}
                - Port field processing rules:
                  1. Origin port: Use pol_code field directly
                  2. Destination port:
                     * Import business: If pod_code (port of discharge) is empty, default to system location {system_location_company['code']}
                     * Export business: Use pod_code field directly
                  3. When analyzing port-related data, must follow the above rules for port field processing
                """

            analysis_prompt = f"""
            Please answer the following question in English: {question}

            Data Description:
            - Current analysis is for {current_company_en} business data (pro2_system_id={pro2_system_id})
            - Current data table: {table_name}
            - Data records count: {len(data_df)} records
            - Data has been preprocessed and can be used directly without additional deduplication

            {table_structure_en}

            {port_conventions_en}

            Important Analysis Rules:
            1. When conducting port-related analysis, must strictly follow the port convention rules for field processing
            2. For null value fields, apply default values according to convention rules
            3. Ensure accuracy and consistency of analysis results

            Please analyze the data and provide concise and clear results. Ensure your code executes correctly and returns results.
            If you need to calculate statistics, please use pandas aggregation functions like sum(), mean(), count(), etc.
            """

        # 执行分析
        logger.info(f"开始执行 AI 分析: {question}")
        result = df.chat(analysis_prompt)

        if result is None or str(result).strip() == "":
            return "AI 分析未能生成有效结果。请尝试重新表述您的问题。"

        logger.info("AI 分析完成")
        return str(result)

    except Exception as e:
        logger.error(f"PandasAI 分析失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")

        if "API" in str(e) or "authentication" in str(e).lower():
            return f"AI 服务连接失败: {str(e)}。请检查 API 配置。"
        elif "timeout" in str(e).lower():
            return "AI 分析超时。请尝试简化问题或缩小数据范围。"
        else:
            return f"AI 分析过程中出现错误: {str(e)}"

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler that manages profit data scheduler startup and shutdown."""
    global _profit_scheduler

    # Startup
    # 检查是否启用自动提取功能
    enable_auto_extract = os.getenv('ENABLE_AUTO_EXTRACT', 'False').lower() == 'true'

    if enable_auto_extract:
        try:
            # 导入调度器类（延迟导入避免循环依赖）
            from ..basic.profit_data_scheduler import ProfitDataScheduler

            _profit_scheduler = ProfitDataScheduler()
            logger.info("正在启动利润数据周期性调度器...")

            # 在后台启动调度器
            _profit_scheduler.current_task = asyncio.create_task(_profit_scheduler.start_scheduler())

            logger.info("✅ 利润数据调度器已启动（后台运行）")
            logger.info("📋 调度规则：")
            logger.info("   • 距今2年以上：每3个月检查一次")
            logger.info("   • 距今1-2年：每3个月检查一次")
            logger.info("   • 距今3个月-1年：每1个月检查一次")
            logger.info("   • 距今1-3个月：每天检查一次")
            logger.info("   • 3个月数据周期，自动增量更新")
            logger.info("   • 执行时间：每日20:00至次日8:00")

        except Exception as e:
            logger.error(f"❌ 启动利润数据调度器失败: {e}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
    else:
        logger.info("⏸️ 自动提取功能已禁用")
        logger.info("💡 往期job和booking业务数据自动提取功能未启用")
        logger.info("   如需启用，请使用 --enable-auto-extract 参数重启服务器")
    
    yield
    
    # Shutdown
    if _profit_scheduler:
        try:
            logger.info("正在关闭利润数据调度器...")
            _profit_scheduler.is_running = False
            if _profit_scheduler.current_task:
                _profit_scheduler.current_task.cancel()
                try:
                    await _profit_scheduler.current_task
                except asyncio.CancelledError:
                    pass
            logger.info("✅ 利润数据调度器已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭利润数据调度器失败: {e}")

app = FastAPI(
    title="CMS Simplified",
    description="企业内容管理系统精简版本 - 专注数据导出",
    version="4.0.0",
    lifespan=lifespan
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型定义
class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索关键词")
    search_type: str = Field(default="company", description="搜索类型")
    
    @field_validator('search_type')
    @classmethod
    def validate_search_type(cls, v):
        if v not in ['company', 'user', 'department', 'salesman']:
            raise ValueError('搜索类型必须为 company, user, department 或 salesman')
        return v

class DateRangeRequest(BaseModel):
    """日期范围请求模型"""
    begin_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
    format: str = Field(default="excel", description="导出格式")

    @field_validator('begin_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为 YYYY-MM-DD')

    @field_validator('format')
    @classmethod
    def validate_format(cls, v):
        if v.lower() not in ['excel', 'csv']:
            raise ValueError('导出格式必须为 excel 或 csv')
        return v.lower()


class DataAnalysisRequest(BaseModel):
    """数据分析请求模型"""
    begin_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")
    data_type: str = Field(..., description="数据类型: booking 或 job")
    question: str = Field(..., min_length=1, description="分析问题（自然语言）")
    language: str = Field(default="zh", description="返回语言: zh 或 en")

    @field_validator('begin_date', 'end_date')
    @classmethod
    def validate_date_format(cls, v):
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('日期格式必须为 YYYY-MM-DD')

    @field_validator('data_type')
    @classmethod
    def validate_data_type(cls, v):
        if v.lower() not in ['booking', 'job']:
            raise ValueError('数据类型必须为 booking 或 job')
        return v.lower()

    @field_validator('language')
    @classmethod
    def validate_language(cls, v):
        if v.lower() not in ['zh', 'en']:
            raise ValueError('语言必须为 zh 或 en')
        return v.lower()


class SystemStatus(BaseModel):
    """系统状态模型"""
    status: str
    timestamp: datetime
    database_connection: bool
    api_version: str
    feature_count: int


# 健康检查
@app.get("/health", response_model=SystemStatus)
async def health_check():
    """System health check endpoint that verifies database connectivity, API status, and core service availability. Returns comprehensive system status including connection status and feature availability."""
    try:
        await search_company_by_part_name("__test_connection__")
        db_status = True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        db_status = False
    
    return SystemStatus(
        status="healthy" if db_status else "degraded",
        timestamp=datetime.now(),
        database_connection=db_status,
        api_version="4.0.0",
        feature_count=3  # 名称查询、数据导出、健康检查
    )

# 统一搜索
@app.post("/search/", response_model=Dict[str, Any])
async def unified_search(request: SearchRequest):
    """Unified search service for finding companies, users, departments, and salespeople by partial name matching. Supports fuzzy search across all entity types in the logistics management system."""
    logger.info(f"统一搜索请求: 类型={request.search_type}, 关键词={request.query}")
    
    try:
        start_time = datetime.now()
        
        if request.search_type == "company":
            results = await search_company_by_part_name(request.query)
        elif request.search_type == "user":
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "salesman":
            # 业务员搜索映射到用户搜索
            results = await search_user_by_part_name(request.query)
        elif request.search_type == "department":
            results = await search_department_by_part_name(request.query)
        else:
            raise HTTPException(status_code=400, detail="不支持的搜索类型")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return {
            "success": True,
            "search_type": request.search_type,
            "query": request.query,
            "data": results or [],
            "count": len(results) if results else 0,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"统一搜索失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# 导出Booking数据
@app.post("/export/bookings", response_model=Dict[str, Any])
async def export_bookings(request: DateRangeRequest):
    """Export shipping booking data with profit analysis including revenue, costs, and carrier details for specified date range. Returns downloadable Excel/CSV file with TEU calculations and financial metrics."""
    try:
        logger.info(f"开始导出Booking数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_sea_air_profit_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Booking数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Booking数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"bookings_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="booking",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        
        return {
            "success": True,
            "message": "Booking数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Booking数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Booking数据失败: {str(e)}")

# 导出Job数据
@app.post("/export/jobs", response_model=Dict[str, Any])
async def export_jobs(request: DateRangeRequest):
    """Export job order data with detailed logistics operations including container tracking, consolidation details, approval workflows, and business metrics for specified date range. Returns downloadable Excel/CSV file."""
    try:
        logger.info(f"开始导出Job数据: {request.begin_date} 至 {request.end_date}")
        start_time = datetime.now()
        
        try:
            data = await asyncio.wait_for(
                get_job_details_with_transhipment(
                    request.begin_date, 
                    request.end_date
                ),
                timeout=600.0
            )
        except asyncio.TimeoutError:
            logger.error(f"导出Job数据超时: {request.begin_date} 至 {request.end_date}")
            raise HTTPException(
                status_code=408,
                detail="导出Job数据超时，请缩小日期范围或联系管理员"
            )
        
        filename_prefix = f"jobs_{request.begin_date}_{request.end_date}"
        download_url = await export_to_oss(
            data, 
            filename_prefix, 
            data_type="job",
            file_format=request.format
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 获取记录数并检查数据是否为空
        record_count = data.get('total_count', 0) if isinstance(data, dict) else len(data)
        actual_data = data.get('data', []) if isinstance(data, dict) else data
        
        if not actual_data or len(actual_data) == 0:
            logger.warning(f"Job数据导出结果为空: {request.begin_date} 至 {request.end_date}")
            return {
                "success": False,
                "message": "指定日期范围内没有找到Job数据，请检查日期范围或数据库连接",
                "download_url": None,
                "record_count": 0,
                "date_range": f"{request.begin_date} 至 {request.end_date}",
                "format": request.format,
                "execution_time_seconds": execution_time,
                "timestamp": datetime.now().isoformat(),
                "suggestion": "请尝试扩大日期范围或检查数据库连接配置"
            }
        
        return {
            "success": True,
            "message": "Job数据导出成功",
            "download_url": download_url,
            "record_count": record_count,
            "date_range": f"{request.begin_date} 至 {request.end_date}",
            "format": request.format,
            "execution_time_seconds": execution_time,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"导出Job数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出Job数据失败: {str(e)}")


# 数据分析端点
@app.post("/analyze/data", response_model=Dict[str, Any])
async def analyze_data(request: DataAnalysisRequest):
    """AI-powered data analysis service using PandasAI for natural language queries on business data. Supports both booking and job data analysis with intelligent insights, trend analysis, and recommendations in Chinese or English. Features intelligent caching for improved performance in continuous queries."""
    try:
        logger.info(f"开始数据分析: 类型={request.data_type}, 日期={request.begin_date} 至 {request.end_date}, 问题={request.question}")
        start_time = datetime.now()

        logger.info(f"开始AI分析，直接连接MySQL数据库")

        # 执行AI分析
        try:
            analysis_result = await asyncio.wait_for(
                analyze_data_with_pandasai(
                    question=request.question,
                    language=request.language,
                    session_id=None,  # 可以根据需要添加session_id逻辑
                    begin_date=request.begin_date,
                    end_date=request.end_date,
                    data_type=request.data_type,
                    pro2_system_id=86532  # 默认为青岛公司，可以根据需要动态设置
                ),
                timeout=600.0  # 10分钟超时
            )
        except asyncio.TimeoutError:
            logger.error("AI分析超时")
            raise HTTPException(
                status_code=408,
                detail="AI分析超时，请尝试简化问题或缩小数据范围"
            )

        execution_time = (datetime.now() - start_time).total_seconds()

        return {
            "success": True,
            "message": "数据分析完成",
            "analysis_result": analysis_result,
            "question": request.question,
            "language": request.language,
            "data_info": {
                "data_type": request.data_type,
                "date_range": f"{request.begin_date} 至 {request.end_date}",
                "data_source": "MySQL数据库直连",
                "tables": ["t_booking_details", "t_job_details"]
            },
            "performance_info": {
                "total_execution_time_seconds": execution_time,
                "data_source": "MySQL直连（t_booking_details + t_job_details）",
                "deduplication_logic": "相同job_id或job_id+bkbl_id时选择id更大的记录"
            },
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据分析失败: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"数据分析失败: {str(e)}")


# 缓存管理端点
@app.get("/cache/stats", response_model=Dict[str, Any])
async def get_cache_stats():
    """Get data cache statistics and performance metrics for monitoring cache efficiency and hit rates across all endpoints."""
    try:
        stats = global_cache_manager.get_stats()
        return {
            "success": True,
            "message": "全局缓存统计信息获取成功",
            "cache_stats": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")


@app.post("/cache/clear", response_model=Dict[str, Any])
async def clear_cache():
    """Clear all cached data to free memory and force fresh data retrieval for subsequent requests across all endpoints."""
    try:
        old_stats = global_cache_manager.get_stats()
        global_cache_manager.clear()

        return {
            "success": True,
            "message": "全局缓存已清空",
            "cleared_items": old_stats['cache_size'],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"清空缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空缓存失败: {str(e)}")


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
    