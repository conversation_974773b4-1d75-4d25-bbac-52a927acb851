#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试PandasAI分析功能
"""

import os
import asyncio
from datetime import datetime, timedelta
import sys

# 添加项目根目录到Python路径
sys.path.append('/Volumes/PSSD/code_files/mcp-cms')

from utils.fastapi_apps.fastapi_cms_simplified import analyze_data_with_pandasai

async def test_simple_analysis():
    """测试简单的数据分析"""
    try:
        print("🔍 开始测试简单的PandasAI分析...")
        
        # 设置测试参数 - 非常简单的问题
        question = "数据总共有多少条记录？"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 分析完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_port_analysis():
    """测试港口相关的分析"""
    try:
        print("\n🔍 开始测试港口相关分析...")
        
        # 设置测试参数 - 港口相关问题
        question = "统计不同起运港的业务数量，并按照港口约定处理空值"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 港口分析完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 港口分析测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_simple_analysis())
    asyncio.run(test_port_analysis())
