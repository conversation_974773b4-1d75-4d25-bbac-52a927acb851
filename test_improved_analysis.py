#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的分析功能
"""

import os
import asyncio
from datetime import datetime, timedelta
import sys

# 添加项目根目录到Python路径
sys.path.append('/Volumes/PSSD/code_files/mcp-cms')

from utils.fastapi_apps.fastapi_cms_simplified import analyze_data_with_pandasai

async def test_table_structure_understanding():
    """测试表结构理解"""
    try:
        print("🔍 开始测试表结构理解...")
        
        # 设置测试参数 - 测试表结构理解
        question = "根据表结构说明，列出当前数据表的所有主要字段，并说明每个字段的用途"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 表结构理解测试完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 表结构理解测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_port_convention_understanding():
    """测试港口约定理解"""
    try:
        print("\n🔍 开始测试港口约定理解...")
        
        # 设置测试参数 - 测试港口约定理解
        question = "根据港口约定规则，说明在分析起运港和目的港时应该如何处理job_pol、bill_pol和bill_pod字段"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 港口约定理解测试完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 港口约定理解测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_system_location_detection():
    """测试系统所在地检测"""
    try:
        print("\n🔍 开始测试系统所在地检测...")
        
        # 设置测试参数 - 测试系统所在地检测
        question = "当前系统所在地是哪里？在进行进口业务分析时，如果目的港为空应该如何处理？"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="booking",
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ 系统所在地检测测试完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 系统所在地检测测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def test_job_data_analysis():
    """测试Job数据分析"""
    try:
        print("\n🔍 开始测试Job数据分析...")
        
        # 设置测试参数 - 测试Job数据分析
        question = "根据Job数据的表结构，说明pol_code和pod_code字段的作用，以及在港口分析中的处理规则"
        language = "zh"
        
        # 计算日期范围（最近30天）
        end_date = datetime.now().strftime('%Y-%m-%d')
        begin_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"📅 分析日期范围: {begin_date} 至 {end_date}")
        print(f"❓ 分析问题: {question}")
        print(f"🌐 语言: {language}")
        
        # 执行分析
        start_time = datetime.now()
        result = await analyze_data_with_pandasai(
            question=question,
            language=language,
            session_id=None,
            begin_date=begin_date,
            end_date=end_date,
            data_type="job",  # 测试Job数据
            pro2_system_id=86532  # 测试青岛公司数据
        )
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"\n✅ Job数据分析测试完成！")
        print(f"⏱️ 执行时间: {execution_time:.2f} 秒")
        print(f"\n📊 分析结果:")
        print("=" * 80)
        print(result)
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ Job数据分析测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_table_structure_understanding())
    asyncio.run(test_port_convention_understanding())
    asyncio.run(test_system_location_detection())
    asyncio.run(test_job_data_analysis())
